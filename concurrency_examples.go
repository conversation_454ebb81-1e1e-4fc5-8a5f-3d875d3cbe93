// Go 并发编程示例
// Goroutines 和 Channels 是 Go 语言的核心特性
// 对于前端开发者来说，这类似于 JavaScript 的 async/await，但更强大
package main

import (
	"fmt"
	"math/rand"
	"sync"
	"time"
)

// 1. 基础 Goroutine 示例
func demonstrateBasicGoroutines() {
	fmt.Println("\n=== 基础 Goroutine 示例 ===")
	
	// 普通函数调用（同步）
	fmt.Println("🔄 同步执行:")
	sayHello("张三")
	sayHello("李四")
	
	fmt.Println("\n🚀 异步执行 (Goroutines):")
	// 使用 go 关键字启动 goroutine（异步）
	go sayHello("王五")  // 这会立即返回，不等待执行完成
	go sayHello("赵六")
	
	// 等待一下，让 goroutines 有时间执行
	time.Sleep(2 * time.Second)
	fmt.Println("✅ 主程序继续执行")
}

// 辅助函数：模拟耗时操作
func sayHello(name string) {
	for i := 1; i <= 3; i++ {
		fmt.Printf("  👋 %s 说: Hello %d\n", name, i)
		time.Sleep(500 * time.Millisecond) // 模拟耗时操作
	}
}

// 2. Channels 基础示例
func demonstrateBasicChannels() {
	fmt.Println("\n=== Channels 基础示例 ===")
	
	// 创建一个字符串类型的 channel
	// channel 是 goroutines 之间通信的管道
	messages := make(chan string)
	
	// 启动一个 goroutine 发送消息
	go func() {
		time.Sleep(1 * time.Second)
		messages <- "Hello from goroutine!" // 发送消息到 channel
	}()
	
	fmt.Println("📡 等待消息...")
	msg := <-messages // 从 channel 接收消息（会阻塞直到有消息）
	fmt.Printf("📨 收到消息: %s\n", msg)
}

// 3. 带缓冲的 Channels
func demonstrateBufferedChannels() {
	fmt.Println("\n=== 带缓冲的 Channels 示例 ===")
	
	// 创建一个缓冲大小为 3 的 channel
	numbers := make(chan int, 3)
	
	// 发送数据到 channel（不会阻塞，因为有缓冲）
	numbers <- 1
	numbers <- 2
	numbers <- 3
	
	fmt.Println("📤 已发送 3 个数字到缓冲 channel")
	
	// 接收数据
	for i := 0; i < 3; i++ {
		num := <-numbers
		fmt.Printf("📥 接收到数字: %d\n", num)
	}
}

// 4. Channel 方向（只读/只写）
func demonstrateChannelDirections() {
	fmt.Println("\n=== Channel 方向示例 ===")
	
	messages := make(chan string)
	
	// 只能发送的 channel
	go sender(messages)
	
	// 只能接收的 channel
	go receiver(messages)
	
	time.Sleep(2 * time.Second)
}

// 只能发送消息的函数
func sender(ch chan<- string) { // chan<- 表示只能发送
	messages := []string{"消息1", "消息2", "消息3"}
	for _, msg := range messages {
		ch <- msg
		time.Sleep(500 * time.Millisecond)
	}
	close(ch) // 关闭 channel，表示不再发送数据
}

// 只能接收消息的函数
func receiver(ch <-chan string) { // <-chan 表示只能接收
	for msg := range ch { // range 会自动处理 channel 关闭
		fmt.Printf("📨 接收: %s\n", msg)
	}
	fmt.Println("📪 Channel 已关闭")
}

// 5. Select 语句 - 类似于 JavaScript 的 Promise.race()
func demonstrateSelect() {
	fmt.Println("\n=== Select 语句示例 ===")
	
	ch1 := make(chan string)
	ch2 := make(chan string)
	
	// 启动两个 goroutines
	go func() {
		time.Sleep(1 * time.Second)
		ch1 <- "来自 channel 1 的消息"
	}()
	
	go func() {
		time.Sleep(2 * time.Second)
		ch2 <- "来自 channel 2 的消息"
	}()
	
	// select 会等待第一个可用的 channel
	for i := 0; i < 2; i++ {
		select {
		case msg1 := <-ch1:
			fmt.Printf("📨 Channel 1: %s\n", msg1)
		case msg2 := <-ch2:
			fmt.Printf("📨 Channel 2: %s\n", msg2)
		case <-time.After(3 * time.Second): // 超时处理
			fmt.Println("⏰ 超时了!")
		}
	}
}

// 6. Worker Pool 模式 - 类似于线程池
func demonstrateWorkerPool() {
	fmt.Println("\n=== Worker Pool 示例 ===")
	
	// 创建任务和结果的 channels
	jobs := make(chan int, 100)
	results := make(chan int, 100)
	
	// 启动 3 个 worker goroutines
	for w := 1; w <= 3; w++ {
		go worker(w, jobs, results)
	}
	
	// 发送 5 个任务
	for j := 1; j <= 5; j++ {
		jobs <- j
	}
	close(jobs) // 关闭任务 channel
	
	// 收集结果
	for a := 1; a <= 5; a++ {
		result := <-results
		fmt.Printf("📊 任务结果: %d\n", result)
	}
}

// Worker 函数：处理任务
func worker(id int, jobs <-chan int, results chan<- int) {
	for j := range jobs {
		fmt.Printf("👷 Worker %d 开始处理任务 %d\n", id, j)
		
		// 模拟工作（随机耗时）
		time.Sleep(time.Duration(rand.Intn(1000)) * time.Millisecond)
		
		// 发送结果
		results <- j * 2
		
		fmt.Printf("✅ Worker %d 完成任务 %d\n", id, j)
	}
}

// 7. 使用 WaitGroup 等待多个 Goroutines
func demonstrateWaitGroup() {
	fmt.Println("\n=== WaitGroup 示例 ===")
	
	var wg sync.WaitGroup // WaitGroup 用于等待多个 goroutines 完成
	
	tasks := []string{"任务A", "任务B", "任务C", "任务D"}
	
	for _, task := range tasks {
		wg.Add(1) // 增加等待计数
		
		// 启动 goroutine 处理任务
		go func(taskName string) {
			defer wg.Done() // 任务完成时减少计数
			
			fmt.Printf("🔄 开始执行 %s\n", taskName)
			
			// 模拟任务执行时间
			time.Sleep(time.Duration(rand.Intn(2000)) * time.Millisecond)
			
			fmt.Printf("✅ 完成 %s\n", taskName)
		}(task) // 传递参数给匿名函数
	}
	
	fmt.Println("⏳ 等待所有任务完成...")
	wg.Wait() // 等待所有 goroutines 完成
	fmt.Println("🎉 所有任务都完成了!")
}

// 8. 实际应用：并发下载示例
func demonstrateConcurrentDownload() {
	fmt.Println("\n=== 并发下载示例 ===")
	
	urls := []string{
		"https://jsonplaceholder.typicode.com/posts/1",
		"https://jsonplaceholder.typicode.com/posts/2",
		"https://jsonplaceholder.typicode.com/posts/3",
	}
	
	var wg sync.WaitGroup
	results := make(chan string, len(urls))
	
	for i, url := range urls {
		wg.Add(1)
		go func(id int, url string) {
			defer wg.Done()
			
			// 模拟下载（实际项目中这里会是真正的HTTP请求）
			fmt.Printf("📡 开始下载 %d: %s\n", id+1, url)
			time.Sleep(time.Duration(rand.Intn(2000)) * time.Millisecond)
			
			result := fmt.Sprintf("下载 %d 完成", id+1)
			results <- result
			fmt.Printf("✅ %s\n", result)
		}(i, url)
	}
	
	// 等待所有下载完成
	wg.Wait()
	close(results)
	
	fmt.Println("\n📋 下载结果:")
	for result := range results {
		fmt.Printf("  %s\n", result)
	}
}

// 主函数：运行所有并发示例
func runConcurrencyExamples() {
	fmt.Println("⚡ Go 并发编程示例")
	fmt.Println("这些概念对于构建高性能的后端应用非常重要")
	
	// 设置随机种子
	rand.Seed(time.Now().UnixNano())
	
	demonstrateBasicGoroutines()
	demonstrateBasicChannels()
	demonstrateBufferedChannels()
	demonstrateChannelDirections()
	demonstrateSelect()
	demonstrateWorkerPool()
	demonstrateWaitGroup()
	demonstrateConcurrentDownload()
	
	fmt.Println("\n✅ 所有并发示例运行完成！")
	fmt.Println("💡 提示：Go 的并发模型基于 CSP (Communicating Sequential Processes)")
	fmt.Println("   这比传统的锁和共享内存模型更安全、更易理解")
}
