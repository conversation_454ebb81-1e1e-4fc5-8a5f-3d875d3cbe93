// TODO API - 一个完整的实际项目示例
// 这个项目展示了如何构建一个真实的 REST API
// 包含了数据持久化、错误处理、中间件等实际开发中的常见需求
package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"
)

// Todo 数据结构
type Todo struct {
	ID          int       `json:"id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Completed   bool      `json:"completed"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// API 响应结构
type TodoAPIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Total   int         `json:"total,omitempty"`
}

// 简单的内存数据存储（实际项目中会使用数据库）
type TodoStore struct {
	todos  []Todo
	nextID int
}

// 全局存储实例
var store = &TodoStore{
	todos:  []Todo{},
	nextID: 1,
}

// 数据持久化：保存到文件
func (ts *TodoStore) SaveToFile() error {
	data, err := json.MarshalIndent(ts.todos, "", "  ")
	if err != nil {
		return err
	}
	
	return os.WriteFile("todos.json", data, 0644)
}

// 数据持久化：从文件加载
func (ts *TodoStore) LoadFromFile() error {
	data, err := os.ReadFile("todos.json")
	if err != nil {
		// 如果文件不存在，创建空的 todos 列表
		if os.IsNotExist(err) {
			ts.todos = []Todo{}
			return nil
		}
		return err
	}
	
	err = json.Unmarshal(data, &ts.todos)
	if err != nil {
		return err
	}
	
	// 更新 nextID
	maxID := 0
	for _, todo := range ts.todos {
		if todo.ID > maxID {
			maxID = todo.ID
		}
	}
	ts.nextID = maxID + 1
	
	return nil
}

// CRUD 操作

// 创建 Todo
func (ts *TodoStore) CreateTodo(title, description string) Todo {
	todo := Todo{
		ID:          ts.nextID,
		Title:       title,
		Description: description,
		Completed:   false,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	
	ts.todos = append(ts.todos, todo)
	ts.nextID++
	ts.SaveToFile() // 保存到文件
	
	return todo
}

// 获取所有 Todos
func (ts *TodoStore) GetAllTodos() []Todo {
	return ts.todos
}

// 根据ID获取 Todo
func (ts *TodoStore) GetTodoByID(id int) (*Todo, bool) {
	for i, todo := range ts.todos {
		if todo.ID == id {
			return &ts.todos[i], true
		}
	}
	return nil, false
}

// 更新 Todo
func (ts *TodoStore) UpdateTodo(id int, title, description string, completed *bool) (*Todo, bool) {
	for i, todo := range ts.todos {
		if todo.ID == id {
			if title != "" {
				ts.todos[i].Title = title
			}
			if description != "" {
				ts.todos[i].Description = description
			}
			if completed != nil {
				ts.todos[i].Completed = *completed
			}
			ts.todos[i].UpdatedAt = time.Now()
			
			ts.SaveToFile() // 保存到文件
			return &ts.todos[i], true
		}
	}
	return nil, false
}

// 删除 Todo
func (ts *TodoStore) DeleteTodo(id int) bool {
	for i, todo := range ts.todos {
		if todo.ID == id {
			// 从切片中删除元素
			ts.todos = append(ts.todos[:i], ts.todos[i+1:]...)
			ts.SaveToFile() // 保存到文件
			return true
		}
	}
	return false
}

// 中间件：CORS 和日志
func corsAndLogMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// CORS 设置
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		// 处理预检请求
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}
		
		// 请求日志
		start := time.Now()
		log.Printf("开始处理: %s %s", r.Method, r.URL.Path)
		
		// 执行下一个处理函数
		next(w, r)
		
		// 完成日志
		duration := time.Since(start)
		log.Printf("完成处理: %s %s - 耗时: %v", r.Method, r.URL.Path, duration)
	}
}

// 辅助函数：发送 JSON 响应
func sendTodoResponse(w http.ResponseWriter, statusCode int, response TodoAPIResponse) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(response)
}

// 路由处理函数

// 获取所有 Todos
func getTodosHandler(w http.ResponseWriter, r *http.Request) {
	todos := store.GetAllTodos()
	
	sendTodoResponse(w, http.StatusOK, TodoAPIResponse{
		Success: true,
		Message: "成功获取 Todo 列表",
		Data:    todos,
		Total:   len(todos),
	})
}

// 创建新 Todo
func createTodoHandler(w http.ResponseWriter, r *http.Request) {
	var request struct {
		Title       string `json:"title"`
		Description string `json:"description"`
	}
	
	err := json.NewDecoder(r.Body).Decode(&request)
	if err != nil {
		sendTodoResponse(w, http.StatusBadRequest, TodoAPIResponse{
			Success: false,
			Error:   "无效的 JSON 数据",
		})
		return
	}
	
	// 验证必填字段
	if strings.TrimSpace(request.Title) == "" {
		sendTodoResponse(w, http.StatusBadRequest, TodoAPIResponse{
			Success: false,
			Error:   "标题不能为空",
		})
		return
	}
	
	// 创建 Todo
	todo := store.CreateTodo(request.Title, request.Description)
	
	sendTodoResponse(w, http.StatusCreated, TodoAPIResponse{
		Success: true,
		Message: "Todo 创建成功",
		Data:    todo,
	})
}

// 获取单个 Todo
func getTodoHandler(w http.ResponseWriter, r *http.Request) {
	// 从 URL 路径中提取 ID
	path := r.URL.Path
	parts := strings.Split(path, "/")
	if len(parts) < 4 {
		sendTodoResponse(w, http.StatusBadRequest, TodoAPIResponse{
			Success: false,
			Error:   "缺少 Todo ID",
		})
		return
	}
	
	id, err := strconv.Atoi(parts[3])
	if err != nil {
		sendTodoResponse(w, http.StatusBadRequest, TodoAPIResponse{
			Success: false,
			Error:   "无效的 Todo ID",
		})
		return
	}
	
	todo, found := store.GetTodoByID(id)
	if !found {
		sendTodoResponse(w, http.StatusNotFound, TodoAPIResponse{
			Success: false,
			Error:   "Todo 不存在",
		})
		return
	}
	
	sendTodoResponse(w, http.StatusOK, TodoAPIResponse{
		Success: true,
		Message: "成功获取 Todo",
		Data:    todo,
	})
}

// 更新 Todo
func updateTodoHandler(w http.ResponseWriter, r *http.Request) {
	// 提取 ID
	path := r.URL.Path
	parts := strings.Split(path, "/")
	if len(parts) < 4 {
		sendTodoResponse(w, http.StatusBadRequest, TodoAPIResponse{
			Success: false,
			Error:   "缺少 Todo ID",
		})
		return
	}
	
	id, err := strconv.Atoi(parts[3])
	if err != nil {
		sendTodoResponse(w, http.StatusBadRequest, TodoAPIResponse{
			Success: false,
			Error:   "无效的 Todo ID",
		})
		return
	}
	
	// 解析请求体
	var request struct {
		Title       string `json:"title"`
		Description string `json:"description"`
		Completed   *bool  `json:"completed"`
	}
	
	err = json.NewDecoder(r.Body).Decode(&request)
	if err != nil {
		sendTodoResponse(w, http.StatusBadRequest, TodoAPIResponse{
			Success: false,
			Error:   "无效的 JSON 数据",
		})
		return
	}
	
	// 更新 Todo
	todo, found := store.UpdateTodo(id, request.Title, request.Description, request.Completed)
	if !found {
		sendTodoResponse(w, http.StatusNotFound, TodoAPIResponse{
			Success: false,
			Error:   "Todo 不存在",
		})
		return
	}
	
	sendTodoResponse(w, http.StatusOK, TodoAPIResponse{
		Success: true,
		Message: "Todo 更新成功",
		Data:    todo,
	})
}

// 删除 Todo
func deleteTodoHandler(w http.ResponseWriter, r *http.Request) {
	// 提取 ID
	path := r.URL.Path
	parts := strings.Split(path, "/")
	if len(parts) < 4 {
		sendTodoResponse(w, http.StatusBadRequest, TodoAPIResponse{
			Success: false,
			Error:   "缺少 Todo ID",
		})
		return
	}
	
	id, err := strconv.Atoi(parts[3])
	if err != nil {
		sendTodoResponse(w, http.StatusBadRequest, TodoAPIResponse{
			Success: false,
			Error:   "无效的 Todo ID",
		})
		return
	}
	
	// 删除 Todo
	found := store.DeleteTodo(id)
	if !found {
		sendTodoResponse(w, http.StatusNotFound, TodoAPIResponse{
			Success: false,
			Error:   "Todo 不存在",
		})
		return
	}
	
	sendTodoResponse(w, http.StatusOK, TodoAPIResponse{
		Success: true,
		Message: "Todo 删除成功",
	})
}

// 主路由处理函数
func todosHandler(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case "GET":
		if strings.HasSuffix(r.URL.Path, "/todos") {
			getTodosHandler(w, r)
		} else {
			getTodoHandler(w, r)
		}
	case "POST":
		createTodoHandler(w, r)
	case "PUT":
		updateTodoHandler(w, r)
	case "DELETE":
		deleteTodoHandler(w, r)
	default:
		sendTodoResponse(w, http.StatusMethodNotAllowed, TodoAPIResponse{
			Success: false,
			Error:   "不支持的 HTTP 方法",
		})
	}
}

// 启动 TODO API 服务器
func startTodoAPI() {
	// 加载现有数据
	err := store.LoadFromFile()
	if err != nil {
		log.Printf("加载数据失败: %v", err)
	} else {
		log.Printf("成功加载 %d 个 Todo 项目", len(store.todos))
	}
	
	// 注册路由
	http.HandleFunc("/api/todos", corsAndLogMiddleware(todosHandler))
	http.HandleFunc("/api/todos/", corsAndLogMiddleware(todosHandler))
	
	// 健康检查端点
	http.HandleFunc("/api/health", corsAndLogMiddleware(func(w http.ResponseWriter, r *http.Request) {
		sendTodoResponse(w, http.StatusOK, TodoAPIResponse{
			Success: true,
			Message: "TODO API 运行正常",
			Data: map[string]interface{}{
				"timestamp": time.Now().Format("2006-01-02 15:04:05"),
				"version":   "1.0.0",
				"todos":     len(store.todos),
			},
		})
	}))
	
	port := ":8080"
	fmt.Printf("🚀 TODO API 服务器启动成功！\n")
	fmt.Printf("📍 API 地址: http://localhost%s/api/todos\n", port)
	fmt.Printf("🏥 健康检查: http://localhost%s/api/health\n", port)
	fmt.Printf("💾 数据保存在: todos.json\n")
	fmt.Printf("💡 按 Ctrl+C 停止服务器\n\n")
	
	log.Fatal(http.ListenAndServe(port, nil))
}
