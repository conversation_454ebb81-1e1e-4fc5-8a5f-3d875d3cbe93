// Go 语言测试文件示例
// 测试文件必须以 _test.go 结尾
// 测试函数必须以 Test 开头，并接受 *testing.T 参数
package main

import (
	"fmt"
	"testing"
)

// 测试 add 函数
func TestAdd(t *testing.T) {
	// 测试用例：输入和期望输出
	testCases := []struct {
		a, b     int  // 输入参数
		expected int  // 期望结果
		name     string // 测试用例名称
	}{
		{2, 3, 5, "正数相加"},
		{-1, 1, 0, "负数和正数相加"},
		{0, 0, 0, "零相加"},
		{-5, -3, -8, "负数相加"},
	}
	
	// 遍历测试用例
	for _, tc := range testCases {
		// 使用 t.Run 创建子测试
		t.Run(tc.name, func(t *testing.T) {
			result := add(tc.a, tc.b)
			if result != tc.expected {
				// 如果结果不符合期望，报告错误
				t.Errorf("add(%d, %d) = %d; 期望 %d", tc.a, tc.b, result, tc.expected)
			}
		})
	}
}

// 测试 divide 函数
func TestDivide(t *testing.T) {
	// 测试正常情况
	quotient, remainder := divide(17, 5)
	if quotient != 3 {
		t.Errorf("divide(17, 5) 商 = %d; 期望 3", quotient)
	}
	if remainder != 2 {
		t.Errorf("divide(17, 5) 余数 = %d; 期望 2", remainder)
	}
	
	// 测试边界情况
	quotient, remainder = divide(10, 10)
	if quotient != 1 || remainder != 0 {
		t.Errorf("divide(10, 10) = (%d, %d); 期望 (1, 0)", quotient, remainder)
	}
}

// 基准测试 - 用于性能测试
// 基准测试函数必须以 Benchmark 开头，并接受 *testing.B 参数
func BenchmarkAdd(b *testing.B) {
	// b.N 是基准测试框架提供的迭代次数
	for i := 0; i < b.N; i++ {
		add(100, 200)
	}
}

// 示例测试 - 用于文档和示例
// 示例函数必须以 Example 开头
func ExampleAdd() {
	result := add(2, 3)
	fmt.Println(result)
	// Output: 5
}

// 运行测试的命令：
// go test                    # 运行所有测试
// go test -v                 # 详细输出
// go test -run TestAdd       # 只运行 TestAdd
// go test -bench .           # 运行基准测试
// go test -cover             # 显示代码覆盖率
