// Package 声明 - 每个 Go 文件都必须以 package 声明开始
// main 包是特殊的，它告诉 Go 这是一个可执行程序的入口点
package main

// Import 语句 - 导入需要使用的包
// fmt 包提供了格式化输入输出的功能，类似于其他语言的 printf
import (
	"fmt"
	"os"
	"strconv"
	"strings"
)

// main 函数是程序的入口点，类似于 C/C++ 的 main 函数
// 当运行 Go 程序时，会自动执行 main 函数
func main() {
	// 检查命令行参数
	if len(os.Args) > 1 {
		switch os.Args[1] {
		case "examples":
			runExamples()
		case "practical":
			runPracticalExamples()
		case "concurrency":
			runConcurrencyExamples()
		case "exercises":
			runExercises()
		case "web":
			startWebServer()
		case "todo":
			startTodoAPI()
		case "help":
			showHelp()
		default:
			fmt.Printf("未知命令: %s\n", os.Args[1])
			showHelp()
		}
		return
	}

	// 默认的 Hello World 示例
	fmt.Println("🎉 欢迎来到 Go 语言的世界！")
	fmt.Println("Hello, World! 这是您的第一个 Go 程序")

	// 声明变量的几种方式（Go 语言特色）
	// 方式1：使用 var 关键字声明变量
	var message string = "Go 语言很棒！"
	fmt.Println("变量 message:", message)

	// 方式2：使用短变量声明（:= 操作符）
	// Go 会自动推断变量类型，这是 Go 的一个便利特性
	name := "Go 学习者"
	age := 25
	fmt.Printf("你好，%s！你今年 %d 岁\n", name, age)

	// 常量声明
	const version = "1.0.0"
	fmt.Println("程序版本:", version)

	// 调用自定义函数
	result := add(10, 20)
	fmt.Printf("10 + 20 = %d\n", result)

	// 展示 Go 的多返回值特性
	quotient, remainder := divide(17, 5)
	fmt.Printf("17 ÷ 5 = %d 余 %d\n", quotient, remainder)

	// 显示帮助信息
	fmt.Println("\n" + strings.Repeat("=", 50))
	showHelp()
}

// 自定义函数：加法运算
// func 关键字用于定义函数
// (a, b int) 表示两个参数，都是 int 类型
// int 表示返回值类型
func add(a, b int) int {
	return a + b
}

// 展示 Go 的多返回值特性 - 这是 Go 语言的一个强大特性
// 一个函数可以返回多个值，常用于返回结果和错误信息
func divide(a, b int) (int, int) {
	quotient := a / b    // 商
	remainder := a % b   // 余数
	return quotient, remainder
}

// 显示帮助信息
func showHelp() {
	fmt.Println("📖 Go 语言学习项目 - 使用指南")
	fmt.Println()
	fmt.Println("🚀 运行方式:")
	fmt.Println("  go run main.go [命令]")
	fmt.Println()
	fmt.Println("📋 可用命令:")
	fmt.Println("  (无参数)     - 运行基础 Hello World 示例")
	fmt.Println("  examples     - 运行 Go 语言特性示例")
	fmt.Println("  practical    - 运行实用功能示例（文件、JSON、HTTP）")
	fmt.Println("  concurrency  - 运行并发编程示例（Goroutines、Channels）")
	fmt.Println("  exercises    - 运行学习练习")
	fmt.Println("  web          - 启动简单的 Web 服务器")
	fmt.Println("  todo         - 启动 TODO API 服务器")
	fmt.Println("  help         - 显示此帮助信息")
	fmt.Println()
	fmt.Println("💡 示例:")
	fmt.Println("  go run main.go examples")
	fmt.Println("  go run main.go web")
	fmt.Println("  go run main.go todo")
	fmt.Println()
	fmt.Println("📚 学习建议:")
	fmt.Println("  1. 先运行基础示例了解语法")
	fmt.Println("  2. 然后尝试实用功能示例")
	fmt.Println("  3. 学习并发编程概念")
	fmt.Println("  4. 完成练习巩固知识")
	fmt.Println("  5. 最后尝试 Web 服务器项目")
}
