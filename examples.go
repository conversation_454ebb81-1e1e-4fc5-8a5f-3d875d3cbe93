// 这个文件包含了更多 Go 语言特性的示例
// 注意：这个文件不能直接运行，因为它不包含 main 函数
// 它是为了展示 Go 语言的各种特性而创建的示例代码
package main

import (
	"fmt"
	"strings"
	"time"
)

// 结构体定义 - 类似于其他语言的类或对象
// Go 没有类的概念，但有结构体，可以实现面向对象的特性
type Person struct {
	Name string  // 姓名
	Age  int     // 年龄
	City string  // 城市
}

// 为 Person 结构体定义方法
// (p Person) 是接收者，表示这个方法属于 Person 类型
func (p Person) Introduce() string {
	return fmt.Sprintf("你好，我是 %s，今年 %d 岁，来自 %s", p.Name, p.Age, p.City)
}

// 指针接收者方法 - 可以修改结构体的值
func (p *Person) HaveBirthday() {
	p.Age++
	fmt.Printf("%s 过生日了！现在 %d 岁\n", p.Name, p.Age)
}

// 接口定义 - Go 的接口是隐式实现的
type Speaker interface {
	Speak() string
}

// Dog 结构体
type Dog struct {
	Name string
}

// Dog 实现了 Speaker 接口（隐式实现）
func (d Dog) Speak() string {
	return fmt.Sprintf("%s 说：汪汪！", d.Name)
}

// Cat 结构体
type Cat struct {
	Name string
}

// Cat 也实现了 Speaker 接口
func (c Cat) Speak() string {
	return fmt.Sprintf("%s 说：喵喵！", c.Name)
}

// 演示切片（Slice）的使用
func demonstrateSlices() {
	fmt.Println("\n=== 切片（Slice）示例 ===")
	
	// 创建切片的几种方式
	// 方式1：使用 make 函数
	numbers := make([]int, 3, 5) // 长度为3，容量为5的切片
	numbers[0] = 10
	numbers[1] = 20
	numbers[2] = 30
	fmt.Printf("numbers: %v, 长度: %d, 容量: %d\n", numbers, len(numbers), cap(numbers))
	
	// 方式2：切片字面量
	fruits := []string{"苹果", "香蕉", "橙子"}
	fmt.Printf("fruits: %v\n", fruits)
	
	// 添加元素
	fruits = append(fruits, "葡萄", "草莓")
	fmt.Printf("添加元素后: %v\n", fruits)
	
	// 切片操作
	fmt.Printf("前三个水果: %v\n", fruits[:3])
	fmt.Printf("从第二个开始: %v\n", fruits[1:])
}

// 演示映射（Map）的使用
func demonstrateMaps() {
	fmt.Println("\n=== 映射（Map）示例 ===")
	
	// 创建映射
	scores := make(map[string]int)
	scores["张三"] = 95
	scores["李四"] = 87
	scores["王五"] = 92
	
	// 或者使用映射字面量
	ages := map[string]int{
		"小明": 25,
		"小红": 23,
		"小刚": 27,
	}
	
	fmt.Printf("成绩: %v\n", scores)
	fmt.Printf("年龄: %v\n", ages)
	
	// 检查键是否存在
	if score, exists := scores["张三"]; exists {
		fmt.Printf("张三的成绩是: %d\n", score)
	}
	
	// 遍历映射
	fmt.Println("所有成绩:")
	for name, score := range scores {
		fmt.Printf("  %s: %d\n", name, score)
	}
}

// 演示错误处理
func divide(a, b float64) (float64, error) {
	if b == 0 {
		return 0, fmt.Errorf("除数不能为零")
	}
	return a / b, nil
}

// 演示 Go 的错误处理模式
func demonstrateErrorHandling() {
	fmt.Println("\n=== 错误处理示例 ===")
	
	// 正常情况
	result, err := divide(10, 2)
	if err != nil {
		fmt.Printf("错误: %v\n", err)
	} else {
		fmt.Printf("10 ÷ 2 = %.2f\n", result)
	}
	
	// 错误情况
	result, err = divide(10, 0)
	if err != nil {
		fmt.Printf("错误: %v\n", err)
	} else {
		fmt.Printf("结果: %.2f\n", result)
	}
}

// 演示字符串操作
func demonstrateStrings() {
	fmt.Println("\n=== 字符串操作示例 ===")
	
	text := "Go 语言很棒"
	fmt.Printf("原字符串: %s\n", text)
	fmt.Printf("字符串长度: %d\n", len(text))
	fmt.Printf("转为大写: %s\n", strings.ToUpper(text))
	fmt.Printf("是否包含'语言': %t\n", strings.Contains(text, "语言"))
	
	// 字符串分割和连接
	words := strings.Split("苹果,香蕉,橙子", ",")
	fmt.Printf("分割后: %v\n", words)
	joined := strings.Join(words, " | ")
	fmt.Printf("连接后: %s\n", joined)
}

// 演示时间操作
func demonstrateTime() {
	fmt.Println("\n=== 时间操作示例 ===")
	
	now := time.Now()
	fmt.Printf("当前时间: %s\n", now.Format("2006-01-02 15:04:05"))
	
	// Go 的时间格式化使用特定的参考时间
	// "Mon Jan 2 15:04:05 MST 2006" 对应 "01/02 03:04:05PM '06 -0700"
	fmt.Printf("格式化时间: %s\n", now.Format("2006年01月02日 15:04:05"))
	
	// 时间计算
	tomorrow := now.AddDate(0, 0, 1)
	fmt.Printf("明天: %s\n", tomorrow.Format("2006-01-02"))
}

// 这个函数展示了如何使用上面定义的各种特性
func runExamples() {
	fmt.Println("🎯 Go 语言特性示例")
	
	// 结构体和方法
	fmt.Println("\n=== 结构体和方法示例 ===")
	person := Person{
		Name: "小明",
		Age:  25,
		City: "北京",
	}
	fmt.Println(person.Introduce())
	person.HaveBirthday()
	
	// 接口
	fmt.Println("\n=== 接口示例 ===")
	var speakers []Speaker
	speakers = append(speakers, Dog{Name: "旺财"})
	speakers = append(speakers, Cat{Name: "咪咪"})
	
	for _, speaker := range speakers {
		fmt.Println(speaker.Speak())
	}
	
	// 调用其他示例函数
	demonstrateSlices()
	demonstrateMaps()
	demonstrateErrorHandling()
	demonstrateStrings()
	demonstrateTime()
}
