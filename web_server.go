// 简单的Web服务器示例
// 这对前端开发者来说非常有用，展示了如何创建API端点
package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"
)

// 用户数据结构（模拟数据库）
type WebUser struct {
	ID       int    `json:"id"`
	Name     string `json:"name"`
	Email    string `json:"email"`
	CreateAt string `json:"created_at"`
}

// API响应结构
type WebAPIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// 模拟用户数据库
var users = []WebUser{
	{ID: 1, Name: "张三", Email: "<EMAIL>", CreateAt: "2024-01-01"},
	{ID: 2, Name: "李四", Email: "<EMAIL>", CreateAt: "2024-01-02"},
	{ID: 3, Name: "王五", Email: "<EMAIL>", CreateAt: "2024-01-03"},
}

// 下一个用户ID
var nextUserID = 4

// 中间件：CORS支持（允许前端跨域请求）
func enableCORS(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头，允许前端应用访问
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
	
	// 处理预检请求
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}
}

// 中间件：请求日志
func logRequest(handler http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		
		// 添加CORS支持
		enableCORS(w, r)
		if r.Method == "OPTIONS" {
			return
		}
		
		// 执行实际的处理函数
		handler(w, r)
		
		// 记录请求日志
		duration := time.Since(start)
		log.Printf("%s %s - %v", r.Method, r.URL.Path, duration)
	}
}

// 辅助函数：发送JSON响应
func sendJSONResponse(w http.ResponseWriter, statusCode int, response WebAPIResponse) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(response)
}

// 路由处理函数

// 1. 首页处理函数
func homeHandler(w http.ResponseWriter, r *http.Request) {
	html := `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Go Web 服务器示例</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .endpoint { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .method { font-weight: bold; color: #007bff; }
    </style>
</head>
<body>
    <h1>🚀 Go Web 服务器示例</h1>
    <p>这是一个用 Go 语言创建的简单 Web 服务器，展示了基本的 API 功能。</p>
    
    <h2>📡 可用的 API 端点：</h2>
    
    <div class="endpoint">
        <span class="method">GET</span> <code>/api/users</code> - 获取所有用户
    </div>
    
    <div class="endpoint">
        <span class="method">GET</span> <code>/api/users/{id}</code> - 获取特定用户
    </div>
    
    <div class="endpoint">
        <span class="method">POST</span> <code>/api/users</code> - 创建新用户
    </div>
    
    <div class="endpoint">
        <span class="method">GET</span> <code>/api/health</code> - 健康检查
    </div>
    
    <h2>🧪 测试示例：</h2>
    <p>您可以使用以下命令测试 API：</p>
    <pre>
# 获取所有用户
curl http://localhost:8080/api/users

# 获取特定用户
curl http://localhost:8080/api/users/1

# 创建新用户
curl -X POST http://localhost:8080/api/users \
  -H "Content-Type: application/json" \
  -d '{"name":"新用户","email":"<EMAIL>"}'
    </pre>
    
    <p><strong>服务器时间：</strong> ` + time.Now().Format("2006-01-02 15:04:05") + `</p>
</body>
</html>`
	
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	fmt.Fprint(w, html)
}

// 2. 获取所有用户
func getUsersHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != "GET" {
		sendJSONResponse(w, http.StatusMethodNotAllowed, WebAPIResponse{
			Success: false,
			Error:   "只支持 GET 方法",
		})
		return
	}
	
	sendJSONResponse(w, http.StatusOK, WebAPIResponse{
		Success: true,
		Message: "成功获取用户列表",
		Data:    users,
	})
}

// 3. 获取特定用户
func getUserHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != "GET" {
		sendJSONResponse(w, http.StatusMethodNotAllowed, WebAPIResponse{
			Success: false,
			Error:   "只支持 GET 方法",
		})
		return
	}
	
	// 从URL路径中提取用户ID
	// 这是一个简化的实现，实际项目中建议使用路由库如 gorilla/mux
	path := r.URL.Path
	if len(path) < 11 { // "/api/users/" 的长度
		sendJSONResponse(w, http.StatusBadRequest, WebAPIResponse{
			Success: false,
			Error:   "缺少用户ID",
		})
		return
	}
	
	idStr := path[11:] // 提取ID部分
	id, err := strconv.Atoi(idStr)
	if err != nil {
		sendJSONResponse(w, http.StatusBadRequest, WebAPIResponse{
			Success: false,
			Error:   "无效的用户ID",
		})
		return
	}
	
	// 查找用户
	for _, user := range users {
		if user.ID == id {
			sendJSONResponse(w, http.StatusOK, WebAPIResponse{
				Success: true,
				Message: "成功获取用户信息",
				Data:    user,
			})
			return
		}
	}
	
	sendJSONResponse(w, http.StatusNotFound, WebAPIResponse{
		Success: false,
		Error:   "用户不存在",
	})
}

// 4. 创建新用户
func createUserHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		sendJSONResponse(w, http.StatusMethodNotAllowed, WebAPIResponse{
			Success: false,
			Error:   "只支持 POST 方法",
		})
		return
	}
	
	// 解析请求体
	var newUser struct {
		Name  string `json:"name"`
		Email string `json:"email"`
	}
	
	err := json.NewDecoder(r.Body).Decode(&newUser)
	if err != nil {
		sendJSONResponse(w, http.StatusBadRequest, WebAPIResponse{
			Success: false,
			Error:   "无效的JSON数据",
		})
		return
	}
	
	// 验证数据
	if newUser.Name == "" || newUser.Email == "" {
		sendJSONResponse(w, http.StatusBadRequest, WebAPIResponse{
			Success: false,
			Error:   "姓名和邮箱不能为空",
		})
		return
	}
	
	// 创建用户
	user := WebUser{
		ID:       nextUserID,
		Name:     newUser.Name,
		Email:    newUser.Email,
		CreateAt: time.Now().Format("2006-01-02"),
	}
	
	users = append(users, user)
	nextUserID++
	
	sendJSONResponse(w, http.StatusCreated, WebAPIResponse{
		Success: true,
		Message: "用户创建成功",
		Data:    user,
	})
}

// 5. 健康检查
func healthHandler(w http.ResponseWriter, r *http.Request) {
	sendJSONResponse(w, http.StatusOK, WebAPIResponse{
		Success: true,
		Message: "服务器运行正常",
		Data: map[string]interface{}{
			"timestamp": time.Now().Format("2006-01-02 15:04:05"),
			"version":   "1.0.0",
			"users":     len(users),
		},
	})
}

// 启动Web服务器
func startWebServer() {
	// 注册路由处理函数
	http.HandleFunc("/", logRequest(homeHandler))
	http.HandleFunc("/api/users", logRequest(func(w http.ResponseWriter, r *http.Request) {
		if r.Method == "GET" {
			getUsersHandler(w, r)
		} else if r.Method == "POST" {
			createUserHandler(w, r)
		} else {
			sendJSONResponse(w, http.StatusMethodNotAllowed, WebAPIResponse{
				Success: false,
				Error:   "不支持的HTTP方法",
			})
		}
	}))
	
	// 处理带ID的用户请求
	http.HandleFunc("/api/users/", logRequest(getUserHandler))
	http.HandleFunc("/api/health", logRequest(healthHandler))
	
	// 启动服务器
	port := ":8080"
	fmt.Printf("🚀 Web服务器启动成功！\n")
	fmt.Printf("📍 访问地址: http://localhost%s\n", port)
	fmt.Printf("📡 API地址: http://localhost%s/api/users\n", port)
	fmt.Printf("💡 按 Ctrl+C 停止服务器\n\n")
	
	// 启动HTTP服务器
	log.Fatal(http.ListenAndServe(port, nil))
}
