// Go 语言学习练习
// 这些练习帮助巩固 Go 语言的核心概念
// 每个练习都有详细的说明和参考答案
package main

import (
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"
)

// ==================== 练习 1: 基础语法 ====================

// 练习1.1: 变量和常量
func exercise1_1() {
	fmt.Println("\n=== 练习 1.1: 变量和常量 ===")
	fmt.Println("任务：声明不同类型的变量并进行基本运算")
	
	// TODO: 声明一个整数变量 age，值为 25
	age := 25
	
	// TODO: 声明一个字符串变量 name，值为你的名字
	name := "学习者"
	
	// TODO: 声明一个布尔变量 isStudent，值为 true
	isStudent := true
	
	// TODO: 声明一个常量 PI，值为 3.14159
	const PI = 3.14159
	
	// TODO: 计算圆的面积，半径为 5
	radius := 5.0
	area := PI * radius * radius
	
	fmt.Printf("姓名: %s, 年龄: %d, 是学生: %t\n", name, age, isStudent)
	fmt.Printf("半径为 %.1f 的圆的面积: %.2f\n", radius, area)
}

// 练习1.2: 控制结构
func exercise1_2() {
	fmt.Println("\n=== 练习 1.2: 控制结构 ===")
	fmt.Println("任务：使用 if、for、switch 语句")
	
	// TODO: 写一个函数判断数字是正数、负数还是零
	checkNumber := func(num int) string {
		if num > 0 {
			return "正数"
		} else if num < 0 {
			return "负数"
		} else {
			return "零"
		}
	}
	
	numbers := []int{5, -3, 0, 10, -7}
	for _, num := range numbers {
		fmt.Printf("%d 是 %s\n", num, checkNumber(num))
	}
	
	// TODO: 使用 switch 语句判断星期几
	day := time.Now().Weekday()
	switch day {
	case time.Monday:
		fmt.Println("今天是星期一，新的一周开始！")
	case time.Friday:
		fmt.Println("今天是星期五，周末快到了！")
	case time.Saturday, time.Sunday:
		fmt.Println("今天是周末，好好休息！")
	default:
		fmt.Printf("今天是%s\n", day)
	}
}

// ==================== 练习 2: 数据结构 ====================

// 练习2.1: 切片操作
func exercise2_1() {
	fmt.Println("\n=== 练习 2.1: 切片操作 ===")
	fmt.Println("任务：实现切片的各种操作")
	
	// TODO: 创建一个包含 1-10 的切片
	numbers := make([]int, 10)
	for i := 0; i < 10; i++ {
		numbers[i] = i + 1
	}
	fmt.Printf("原始切片: %v\n", numbers)
	
	// TODO: 获取前 5 个元素
	first5 := numbers[:5]
	fmt.Printf("前 5 个元素: %v\n", first5)
	
	// TODO: 获取后 5 个元素
	last5 := numbers[5:]
	fmt.Printf("后 5 个元素: %v\n", last5)
	
	// TODO: 添加新元素
	numbers = append(numbers, 11, 12, 13)
	fmt.Printf("添加元素后: %v\n", numbers)
	
	// TODO: 删除索引为 2 的元素
	index := 2
	numbers = append(numbers[:index], numbers[index+1:]...)
	fmt.Printf("删除索引 %d 后: %v\n", index, numbers)
}

// 练习2.2: 映射操作
func exercise2_2() {
	fmt.Println("\n=== 练习 2.2: 映射操作 ===")
	fmt.Println("任务：实现学生成绩管理系统")
	
	// TODO: 创建学生成绩映射
	scores := map[string]int{
		"张三": 85,
		"李四": 92,
		"王五": 78,
		"赵六": 96,
	}
	
	// TODO: 添加新学生
	scores["钱七"] = 88
	
	// TODO: 计算平均分
	total := 0
	count := 0
	for _, score := range scores {
		total += score
		count++
	}
	average := float64(total) / float64(count)
	
	fmt.Printf("学生成绩: %v\n", scores)
	fmt.Printf("平均分: %.2f\n", average)
	
	// TODO: 找出最高分和最低分
	maxScore := 0
	minScore := 100
	maxStudent := ""
	minStudent := ""
	
	for student, score := range scores {
		if score > maxScore {
			maxScore = score
			maxStudent = student
		}
		if score < minScore {
			minScore = score
			minStudent = student
		}
	}
	
	fmt.Printf("最高分: %s (%d)\n", maxStudent, maxScore)
	fmt.Printf("最低分: %s (%d)\n", minStudent, minScore)
}

// ==================== 练习 3: 结构体和方法 ====================

// 学生结构体
type Student struct {
	Name    string
	Age     int
	Grades  []int
	Average float64
}

// TODO: 为 Student 实现方法
func (s *Student) AddGrade(grade int) {
	s.Grades = append(s.Grades, grade)
	s.calculateAverage()
}

func (s *Student) calculateAverage() {
	if len(s.Grades) == 0 {
		s.Average = 0
		return
	}
	
	total := 0
	for _, grade := range s.Grades {
		total += grade
	}
	s.Average = float64(total) / float64(len(s.Grades))
}

func (s Student) GetInfo() string {
	return fmt.Sprintf("学生: %s, 年龄: %d, 成绩: %v, 平均分: %.2f",
		s.Name, s.Age, s.Grades, s.Average)
}

// 练习3.1: 结构体和方法
func exercise3_1() {
	fmt.Println("\n=== 练习 3.1: 结构体和方法 ===")
	fmt.Println("任务：创建学生管理系统")
	
	// TODO: 创建学生实例
	student := Student{
		Name: "小明",
		Age:  20,
	}
	
	// TODO: 添加成绩
	grades := []int{85, 92, 78, 96, 88}
	for _, grade := range grades {
		student.AddGrade(grade)
	}
	
	fmt.Println(student.GetInfo())
}

// ==================== 练习 4: 接口 ====================

// 形状接口
type Shape interface {
	Area() float64
	Perimeter() float64
}

// 矩形
type Rectangle struct {
	Width, Height float64
}

func (r Rectangle) Area() float64 {
	return r.Width * r.Height
}

func (r Rectangle) Perimeter() float64 {
	return 2 * (r.Width + r.Height)
}

// 圆形
type Circle struct {
	Radius float64
}

func (c Circle) Area() float64 {
	return math.Pi * c.Radius * c.Radius
}

func (c Circle) Perimeter() float64 {
	return 2 * math.Pi * c.Radius
}

// 练习4.1: 接口实现
func exercise4_1() {
	fmt.Println("\n=== 练习 4.1: 接口实现 ===")
	fmt.Println("任务：实现不同形状的面积和周长计算")
	
	// TODO: 创建不同形状的实例
	shapes := []Shape{
		Rectangle{Width: 5, Height: 3},
		Circle{Radius: 4},
		Rectangle{Width: 2, Height: 8},
		Circle{Radius: 2.5},
	}
	
	// TODO: 计算总面积和总周长
	totalArea := 0.0
	totalPerimeter := 0.0
	
	for i, shape := range shapes {
		area := shape.Area()
		perimeter := shape.Perimeter()
		
		totalArea += area
		totalPerimeter += perimeter
		
		fmt.Printf("形状 %d: 面积=%.2f, 周长=%.2f\n", i+1, area, perimeter)
	}
	
	fmt.Printf("总面积: %.2f, 总周长: %.2f\n", totalArea, totalPerimeter)
}

// ==================== 练习 5: 错误处理 ====================

// 自定义错误类型
type DivisionError struct {
	Dividend int
	Divisor  int
}

func (e DivisionError) Error() string {
	return fmt.Sprintf("除法错误: %d ÷ %d (除数不能为零)", e.Dividend, e.Divisor)
}

// 安全除法函数
func safeDivide(a, b int) (float64, error) {
	if b == 0 {
		return 0, DivisionError{Dividend: a, Divisor: b}
	}
	return float64(a) / float64(b), nil
}

// 练习5.1: 错误处理
func exercise5_1() {
	fmt.Println("\n=== 练习 5.1: 错误处理 ===")
	fmt.Println("任务：实现安全的数学运算")
	
	// TODO: 测试除法运算
	testCases := [][]int{
		{10, 2},
		{15, 3},
		{7, 0},  // 这会产生错误
		{20, 4},
		{8, 0},  // 这也会产生错误
	}
	
	for _, testCase := range testCases {
		a, b := testCase[0], testCase[1]
		result, err := safeDivide(a, b)
		
		if err != nil {
			fmt.Printf("❌ %v\n", err)
		} else {
			fmt.Printf("✅ %d ÷ %d = %.2f\n", a, b, result)
		}
	}
}

// ==================== 练习 6: 字符串处理 ====================

// 练习6.1: 字符串操作
func exercise6_1() {
	fmt.Println("\n=== 练习 6.1: 字符串操作 ===")
	fmt.Println("任务：实现文本处理功能")
	
	text := "Go语言是Google开发的编程语言，简洁高效"
	
	// TODO: 字符串长度
	fmt.Printf("文本长度: %d 字符\n", len([]rune(text))) // 使用 rune 正确计算中文字符
	
	// TODO: 转换大小写
	fmt.Printf("转大写: %s\n", strings.ToUpper(text))
	fmt.Printf("转小写: %s\n", strings.ToLower(text))
	
	// TODO: 查找和替换
	if strings.Contains(text, "Google") {
		fmt.Println("✅ 文本包含 'Google'")
	}
	
	newText := strings.Replace(text, "Google", "谷歌", -1)
	fmt.Printf("替换后: %s\n", newText)
	
	// TODO: 分割和连接
	words := strings.Split(text, "，")
	fmt.Printf("分割结果: %v\n", words)
	
	joined := strings.Join(words, " | ")
	fmt.Printf("连接结果: %s\n", joined)
}

// ==================== 练习 7: 综合应用 ====================

// 练习7.1: 数字处理器
func exercise7_1() {
	fmt.Println("\n=== 练习 7.1: 数字处理器 ===")
	fmt.Println("任务：实现数字统计和排序功能")
	
	// TODO: 解析数字字符串
	numberStrings := []string{"42", "17", "8", "93", "25", "invalid", "67", "3"}
	var numbers []int
	var errors []string
	
	for _, str := range numberStrings {
		if num, err := strconv.Atoi(str); err == nil {
			numbers = append(numbers, num)
		} else {
			errors = append(errors, str)
		}
	}
	
	fmt.Printf("有效数字: %v\n", numbers)
	fmt.Printf("无效输入: %v\n", errors)
	
	// TODO: 统计信息
	if len(numbers) > 0 {
		// 排序
		sort.Ints(numbers)
		fmt.Printf("排序后: %v\n", numbers)
		
		// 统计
		sum := 0
		for _, num := range numbers {
			sum += num
		}
		
		average := float64(sum) / float64(len(numbers))
		min := numbers[0]
		max := numbers[len(numbers)-1]
		
		fmt.Printf("总和: %d\n", sum)
		fmt.Printf("平均值: %.2f\n", average)
		fmt.Printf("最小值: %d\n", min)
		fmt.Printf("最大值: %d\n", max)
	}
}

// 运行所有练习
func runExercises() {
	fmt.Println("📚 Go 语言学习练习")
	fmt.Println("这些练习涵盖了 Go 语言的核心概念")
	fmt.Println("建议按顺序完成，每个练习都有详细的说明")
	
	exercise1_1()
	exercise1_2()
	exercise2_1()
	exercise2_2()
	exercise3_1()
	exercise4_1()
	exercise5_1()
	exercise6_1()
	exercise7_1()
	
	fmt.Println("\n🎉 所有练习完成！")
	fmt.Println("💡 建议：")
	fmt.Println("   1. 尝试修改代码，观察不同的结果")
	fmt.Println("   2. 添加更多的测试用例")
	fmt.Println("   3. 实现自己的变体版本")
	fmt.Println("   4. 结合其他示例文件中的概念")
}
